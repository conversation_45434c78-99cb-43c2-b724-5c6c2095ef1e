{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Profile Settings{% endblock %}
{% block page_title %}Profile Settings{% endblock %}
{% block page_description %}Manage your personal information and account settings{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/profile-forms.css' %}">
<link rel="stylesheet" href="{% static 'css/global-animations.css' %}">
<link rel="stylesheet" href="{% static 'css/enhanced-inputs.css' %}">
<link rel="stylesheet" href="{% static 'css/enhanced-profile.css' %}">
{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 font-raleway">Profile</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
<!-- Modern Profile Hero Section with Enhanced Glassmorphism -->
<div class="profile-hero-wrapper mb-8 animate-fade-in-up">
    <div class="profile-hero-container relative overflow-hidden rounded-3xl">
        <!-- Dynamic Background Pattern -->
        <div class="absolute inset-0 bg-gradient-to-br from-harrier-red via-harrier-dark to-harrier-blue"></div>

        <!-- Animated Background Elements -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 right-0 w-96 h-96 bg-white rounded-full blur-3xl transform translate-x-48 -translate-y-48 animate-pulse"></div>
            <div class="absolute bottom-0 left-0 w-80 h-80 bg-harrier-blue rounded-full blur-3xl transform -translate-x-40 translate-y-40 animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-white rounded-full blur-2xl transform -translate-x-32 -translate-y-32 animate-pulse" style="animation-delay: 2s;"></div>
        </div>

        <!-- Glassmorphism Overlay -->
        <div class="absolute inset-0 backdrop-blur-sm bg-gradient-to-r from-white/5 via-transparent to-white/5"></div>

        <!-- Hero Content -->
        <div class="relative z-10 p-8 lg:p-12">
            <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-8">
                <!-- Profile Information Section -->
                <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6 lg:gap-8">
                    <!-- Enhanced Profile Picture Container -->
                    <div class="relative profile-avatar-section group">
                        <div class="profile-avatar-container relative">
                            {% if user.profile_picture %}
                                <img class="profile-avatar object-cover shadow-2xl border-4 border-white/20"
                                     src="{{ user.profile_picture.url }}"
                                     alt="{{ user.get_full_name|default:user.username }}"
                                     id="profilePreview">
                            {% else %}
                                <div class="profile-avatar bg-white/20 backdrop-blur-sm shadow-2xl border-4 border-white/20 flex items-center justify-center text-white font-bold text-4xl font-montserrat" id="profilePreview">
                                    {{ user.first_name|first|default:user.username|first|upper }}
                                </div>
                            {% endif %}

                            <!-- Profile Picture Edit Overlay -->
                            <div class="profile-edit-overlay group-hover:opacity-100 transition-all duration-300">
                                <div class="profile-edit-content">
                                    <i class="fas fa-camera text-white text-xl mb-2"></i>
                                    <span class="text-white text-sm font-medium">Change Photo</span>
                                </div>
                            </div>

                            <!-- Status Indicators -->
                            <div class="absolute -bottom-2 -right-2 flex gap-1">
                                <!-- Online Status -->
                                <div class="status-indicator bg-green-500 border-4 border-white shadow-lg">
                                    <i class="fas fa-check text-white text-xs"></i>
                                </div>
                                {% if user.is_email_verified %}
                                    <!-- Verified Badge -->
                                    <div class="status-indicator bg-blue-500 border-4 border-white shadow-lg">
                                        <i class="fas fa-shield-check text-white text-xs"></i>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Profile Completion Ring -->
                        <div class="absolute -inset-2 rounded-full">
                            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/>
                                <circle cx="50" cy="50" r="45" fill="none" stroke="rgba(255,255,255,0.8)" stroke-width="2"
                                        stroke-dasharray="283" stroke-dashoffset="113" stroke-linecap="round"
                                        class="transition-all duration-1000 ease-out"/>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-white text-xs font-bold bg-black/20 rounded-full px-2 py-1 backdrop-blur-sm">60%</span>
                            </div>
                        </div>
                    </div>

                    <!-- User Information -->
                    <div class="text-white space-y-4">
                        <!-- Name and Title -->
                        <div class="space-y-3">
                            <h1 class="text-4xl lg:text-5xl font-bold font-montserrat bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent leading-tight">
                                {{ user.first_name|default:"" }} {{ user.last_name|default:"" }}
                                {% if not user.first_name and not user.last_name %}
                                    {{ user.username }}
                                {% endif %}
                            </h1>

                            <!-- Role and Status Badges -->
                            <div class="flex flex-wrap items-center gap-3">
                                <span class="role-badge role-badge-{{ user.role }}">
                                    <i class="fas fa-user-tag mr-2"></i>
                                    {{ user.get_role_display }}
                                </span>

                                {% if user.is_email_verified %}
                                    <span class="status-badge status-verified">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Email Verified
                                    </span>
                                {% else %}
                                    <span class="status-badge status-pending">
                                        <i class="fas fa-exclamation-circle mr-1"></i>
                                        Verify Email
                                    </span>
                                {% endif %}

                                {% if user.role == 'vendor' and vendor %}
                                    {% if vendor.is_approved %}
                                        <span class="status-badge status-business-verified">
                                            <i class="fas fa-shield-alt mr-1"></i>
                                            Verified Business
                                        </span>
                                    {% else %}
                                        <span class="status-badge status-pending">
                                            <i class="fas fa-clock mr-1"></i>
                                            Pending Approval
                                        </span>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>

                        <!-- Company Information (for vendors) -->
                        {% if user.role == 'vendor' and vendor %}
                            <div class="vendor-info space-y-2">
                                <h3 class="text-xl font-semibold text-blue-200 font-raleway">{{ vendor.company_name }}</h3>
                                {% if vendor.business_type %}
                                    <p class="text-blue-100 text-sm">{{ vendor.business_type }}</p>
                                {% endif %}
                                {% if vendor.year_established %}
                                    <p class="text-blue-100 text-sm">Established {{ vendor.year_established }}</p>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Bio Section -->
                        {% if user.bio %}
                            <div class="bio-section">
                                <p class="text-blue-100 text-base leading-relaxed max-w-2xl font-raleway">
                                    {{ user.bio|truncatewords:25 }}
                                </p>
                            </div>
                        {% endif %}

                        <!-- Contact Information -->
                        <div class="contact-info flex flex-wrap items-center gap-4 text-sm">
                            {% if user.email %}
                                <div class="contact-item">
                                    <i class="fas fa-envelope mr-2 text-blue-300"></i>
                                    <span class="text-blue-100">{{ user.email }}</span>
                                </div>
                            {% endif %}
                            {% if user.phone %}
                                <div class="contact-item">
                                    <i class="fas fa-phone mr-2 text-blue-300"></i>
                                    <span class="text-blue-100">{{ user.phone }}</span>
                                </div>
                            {% endif %}
                            {% if user.city %}
                                <div class="contact-item">
                                    <i class="fas fa-map-marker-alt mr-2 text-blue-300"></i>
                                    <span class="text-blue-100">{{ user.city }}, {{ user.country }}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Action Buttons and Quick Stats -->
                <div class="profile-actions-section flex flex-col lg:flex-row items-start lg:items-center gap-6">
                    <!-- Quick Stats Cards -->
                    <div class="stats-grid grid grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Profile Completion -->
                        <div class="stat-card glassmorphism">
                            <div class="stat-content">
                                <div class="stat-icon bg-gradient-to-br from-green-400 to-green-600">
                                    <i class="fas fa-user-check text-white"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-2xl font-montserrat">60%</p>
                                    <p class="stat-label text-blue-100 text-sm">Complete</p>
                                </div>
                            </div>
                        </div>

                        <!-- Member Since -->
                        <div class="stat-card glassmorphism">
                            <div class="stat-content">
                                <div class="stat-icon bg-gradient-to-br from-blue-400 to-blue-600">
                                    <i class="fas fa-calendar text-white"></i>
                                </div>
                                <div class="stat-details">
                                    <p class="stat-value text-white font-bold text-lg font-montserrat">{{ user.date_joined|date:"M Y" }}</p>
                                    <p class="stat-label text-blue-100 text-sm">Member Since</p>
                                </div>
                            </div>
                        </div>

                        <!-- Role-specific Stats -->
                        {% if user.role == 'vendor' and vendor %}
                            <div class="stat-card glassmorphism">
                                <div class="stat-content">
                                    <div class="stat-icon bg-gradient-to-br from-purple-400 to-purple-600">
                                        <i class="fas fa-car text-white"></i>
                                    </div>
                                    <div class="stat-details">
                                        <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ vendor.total_cars|default:0 }}</p>
                                        <p class="stat-label text-blue-100 text-sm">Listings</p>
                                    </div>
                                </div>
                            </div>
                        {% elif user.role == 'customer' %}
                            <div class="stat-card glassmorphism">
                                <div class="stat-content">
                                    <div class="stat-icon bg-gradient-to-br from-orange-400 to-orange-600">
                                        <i class="fas fa-shopping-cart text-white"></i>
                                    </div>
                                    <div class="stat-details">
                                        <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ user.orders.count|default:0 }}</p>
                                        <p class="stat-label text-blue-100 text-sm">Orders</p>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <div class="stat-card glassmorphism">
                                <div class="stat-content">
                                    <div class="stat-icon bg-gradient-to-br from-red-400 to-red-600">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <div class="stat-details">
                                        <p class="stat-value text-white font-bold text-2xl font-montserrat">{{ total_users|default:0 }}</p>
                                        <p class="stat-label text-blue-100 text-sm">Users</p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons flex flex-col gap-3">
                        <button type="button" class="action-btn action-btn-primary" onclick="scrollToSection('personal-tab')">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Profile
                        </button>

                        {% if user.role == 'vendor' %}
                            <a href="{% url 'core:vendor_profile' %}" class="action-btn action-btn-secondary">
                                <i class="fas fa-building mr-2"></i>
                                Business Profile
                            </a>
                        {% endif %}

                        <button type="button" class="action-btn action-btn-outline" onclick="scrollToSection('security-tab')">
                            <i class="fas fa-shield-alt mr-2"></i>
                            Security
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Profile Completion Banner -->
<div class="profile-completion-banner mb-8 animate-fade-in-up" style="animation-delay: 0.2s;">
    <div class="completion-card glassmorphism bg-gradient-to-r from-harrier-blue/10 to-harrier-red/10 border border-white/20">
        <div class="flex items-center justify-between p-6">
            <div class="flex items-center gap-4">
                <div class="completion-icon">
                    <i class="fas fa-chart-line text-harrier-blue text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Complete Your Profile</h3>
                    <p class="text-gray-600 text-sm font-raleway">Add more information to improve your profile visibility and credibility</p>
                </div>
            </div>
            <div class="completion-progress">
                <div class="progress-circle" data-progress="60">
                    <svg class="progress-ring" width="60" height="60">
                        <circle class="progress-ring-circle" stroke="#e5e7eb" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                        <circle class="progress-ring-circle progress-ring-fill" stroke="#DC2626" stroke-width="4" fill="transparent" r="26" cx="30" cy="30"/>
                    </svg>
                    <div class="progress-text">
                        <span class="text-sm font-bold text-harrier-dark">60%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="completion-actions border-t border-white/20 p-4">
            <div class="flex flex-wrap gap-2">
                {% if not user.profile_picture %}
                    <span class="completion-task">
                        <i class="fas fa-camera mr-1"></i>Add Photo
                    </span>
                {% endif %}
                {% if not user.bio %}
                    <span class="completion-task">
                        <i class="fas fa-edit mr-1"></i>Add Bio
                    </span>
                {% endif %}
                {% if not user.phone %}
                    <span class="completion-task">
                        <i class="fas fa-phone mr-1"></i>Add Phone
                    </span>
                {% endif %}
                {% if user.role == 'vendor' and vendor and not vendor.company_logo %}
                    <span class="completion-task">
                        <i class="fas fa-building mr-1"></i>Add Logo
                    </span>
                {% endif %}
            </div>
        </div>
    </div>
</div>
<!-- Enhanced Profile Navigation with Modern Design -->
<div class="profile-navigation-wrapper mb-8 animate-fade-in-up" style="animation-delay: 0.3s;">
    <div class="navigation-container glassmorphism bg-white/80 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
        <!-- Navigation Header -->
        <div class="nav-header bg-gradient-to-r from-harrier-red/5 to-harrier-blue/5 p-6 border-b border-white/20">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-bold text-harrier-dark font-montserrat">Profile Settings</h2>
                    <p class="text-gray-600 text-sm font-raleway">Manage your account information and preferences</p>
                </div>
                <div class="nav-actions flex gap-2">
                    <button type="button" class="nav-action-btn" onclick="saveProfile()">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                    <button type="button" class="nav-action-btn nav-action-secondary" onclick="resetForm()">
                        <i class="fas fa-undo mr-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Tab Navigation -->
        <div class="tab-navigation-container">
            <nav class="tab-navigation" role="tablist">
                <!-- Personal Information Tab -->
                <button type="button"
                        class="nav-tab active"
                        data-tab="personal"
                        role="tab"
                        aria-selected="true"
                        aria-controls="personal-tab">
                    <div class="tab-content">
                        <div class="tab-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title">Personal</span>
                            <span class="tab-subtitle">Basic information</span>
                        </div>
                    </div>
                    <div class="tab-indicator"></div>
                </button>

                <!-- Contact Information Tab -->
                <button type="button"
                        class="nav-tab"
                        data-tab="contact"
                        role="tab"
                        aria-selected="false"
                        aria-controls="contact-tab">
                    <div class="tab-content">
                        <div class="tab-icon">
                            <i class="fas fa-address-book"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title">Contact</span>
                            <span class="tab-subtitle">Communication details</span>
                        </div>
                    </div>
                    <div class="tab-indicator"></div>
                </button>

                <!-- Preferences Tab -->
                <button type="button"
                        class="nav-tab"
                        data-tab="preferences"
                        role="tab"
                        aria-selected="false"
                        aria-controls="preferences-tab">
                    <div class="tab-content">
                        <div class="tab-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title">Preferences</span>
                            <span class="tab-subtitle">Account settings</span>
                        </div>
                    </div>
                    <div class="tab-indicator"></div>
                </button>

                <!-- Security Tab -->
                <button type="button"
                        class="nav-tab"
                        data-tab="security"
                        role="tab"
                        aria-selected="false"
                        aria-controls="security-tab">
                    <div class="tab-content">
                        <div class="tab-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title">Security</span>
                            <span class="tab-subtitle">Password & privacy</span>
                        </div>
                    </div>
                    <div class="tab-indicator"></div>
                </button>

                <!-- Business Tab (for vendors only) -->
                {% if user.role == 'vendor' %}
                <button type="button"
                        class="nav-tab"
                        data-tab="business"
                        role="tab"
                        aria-selected="false"
                        aria-controls="business-tab">
                    <div class="tab-content">
                        <div class="tab-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title">Business</span>
                            <span class="tab-subtitle">Company details</span>
                        </div>
                    </div>
                    <div class="tab-indicator"></div>
                </button>
                {% endif %}

                <!-- Analytics Tab (for vendors and admins) -->
                {% if user.role == 'vendor' or user.role == 'admin' %}
                <button type="button"
                        class="nav-tab"
                        data-tab="analytics"
                        role="tab"
                        aria-selected="false"
                        aria-controls="analytics-tab">
                    <div class="tab-content">
                        <div class="tab-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="tab-text">
                            <span class="tab-title">Analytics</span>
                            <span class="tab-subtitle">Performance data</span>
                        </div>
                    </div>
                    <div class="tab-indicator"></div>
                </button>
                {% endif %}
            </nav>

            <!-- Mobile Tab Selector -->
            <div class="mobile-tab-selector lg:hidden">
                <select class="mobile-tab-select" onchange="switchToTab(this.value)">
                    <option value="personal">Personal Information</option>
                    <option value="contact">Contact Details</option>
                    <option value="preferences">Preferences</option>
                    <option value="security">Security Settings</option>
                    {% if user.role == 'vendor' %}
                        <option value="business">Business Information</option>
                    {% endif %}
                    {% if user.role == 'vendor' or user.role == 'admin' %}
                        <option value="analytics">Analytics</option>
                    {% endif %}
                </select>
            </div>
        </div>
    </div>
</div>
<!-- Enhanced Profile Form Container -->
<div class="profile-form-wrapper animate-fade-in-up" style="animation-delay: 0.4s;">
    <!-- HTMX Messages Area -->
    <div id="profile-messages" class="mb-6"></div>

    <!-- HTMX Loading Indicator -->
    <div id="profile-loading" class="htmx-indicator">
        <div class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
            <div class="loading-container glassmorphism bg-white/90 rounded-2xl p-8 flex items-center gap-4 shadow-2xl">
                <div class="loading-spinner"></div>
                <div class="loading-text">
                    <h3 class="text-lg font-semibold text-harrier-dark font-montserrat">Saving Profile</h3>
                    <p class="text-gray-600 text-sm font-raleway">Please wait while we update your information...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Form Container -->
    <div class="form-container glassmorphism bg-white/80 backdrop-blur-lg border border-white/20 rounded-2xl overflow-hidden">
        <form method="post"
              enctype="multipart/form-data"
              id="profileForm"
              class="profile-form"
              hx-post="{% url 'core:profile' %}"
              hx-trigger="submit"
              hx-target="#profile-messages"
              hx-swap="innerHTML"
              hx-indicator="#profile-loading">
            {% csrf_token %}

            <!-- Personal Information Tab Content -->
            <div class="tab-content active" id="personal-tab" role="tabpanel" aria-labelledby="personal-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Personal Information</h2>
                    <p class="tab-description">Update your basic profile information and personal details</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Profile Picture Section -->
                    <div class="form-section profile-picture-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-harrier-red to-harrier-red-dark">
                                <i class="fas fa-camera text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Profile Picture</h3>
                                <p class="section-subtitle">Upload a professional profile picture to personalize your account</p>
                            </div>
                        </div>

                        <div class="picture-upload-container">
                            <div class="current-picture-display">
                                <div class="picture-frame">
                                    {% if user.profile_picture %}
                                        <img class="profile-image"
                                             src="{{ user.profile_picture.url }}"
                                             alt="Current profile picture"
                                             id="profileImagePreview">
                                    {% else %}
                                        <div class="profile-placeholder" id="profileImagePreview">
                                            <i class="fas fa-user text-4xl text-gray-400"></i>
                                        </div>
                                    {% endif %}

                                    <!-- Upload Overlay -->
                                    <div class="upload-overlay">
                                        <div class="upload-content">
                                            <i class="fas fa-camera text-white text-xl mb-2"></i>
                                            <span class="text-white text-sm font-medium">Change Photo</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="upload-controls">
                                <input type="file"
                                       name="profile_picture"
                                       id="profilePictureInput"
                                       accept="image/*"
                                       class="hidden"
                                       onchange="previewProfileImage(this)">

                                <div class="upload-buttons">
                                    <button type="button"
                                            class="upload-btn upload-btn-primary"
                                            onclick="document.getElementById('profilePictureInput').click()">
                                        <i class="fas fa-upload mr-2"></i>
                                        Upload New Photo
                                    </button>

                                    {% if user.profile_picture %}
                                        <button type="button"
                                                class="upload-btn upload-btn-secondary"
                                                onclick="removeProfilePicture()">
                                            <i class="fas fa-trash mr-2"></i>
                                            Remove Photo
                                        </button>
                                    {% endif %}
                                </div>

                                <div class="upload-guidelines">
                                    <p class="text-sm text-gray-600">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        Recommended: Square image, at least 400x400px, max 5MB
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Basic Information Section -->
                    <div class="form-section basic-info-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-harrier-blue to-harrier-blue-light">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Basic Information</h3>
                                <p class="section-subtitle">Your fundamental personal details</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- First Name -->
                            <div class="form-field">
                                <label for="first_name" class="field-label">
                                    First Name <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="first_name"
                                           id="first_name"
                                           value="{{ user.first_name }}"
                                           class="form-input"
                                           placeholder="Enter your first name"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="first_name_feedback"></div>
                            </div>

                            <!-- Last Name -->
                            <div class="form-field">
                                <label for="last_name" class="field-label">
                                    Last Name <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="last_name"
                                           id="last_name"
                                           value="{{ user.last_name }}"
                                           class="form-input"
                                           placeholder="Enter your last name"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-user text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="last_name_feedback"></div>
                            </div>

                            <!-- Username -->
                            <div class="form-field">
                                <label for="username" class="field-label">
                                    Username <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="username"
                                           id="username"
                                           value="{{ user.username }}"
                                           class="form-input"
                                           placeholder="Choose a unique username"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-at text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="username_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This will be your unique identifier on the platform
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="form-field">
                                <label for="email" class="field-label">
                                    Email Address <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="email"
                                           name="email"
                                           id="email"
                                           value="{{ user.email }}"
                                           class="form-input"
                                           placeholder="Enter your email address"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-envelope text-gray-400"></i>
                                    </div>
                                    {% if user.is_email_verified %}
                                        <div class="input-status status-verified">
                                            <i class="fas fa-check-circle text-green-500"></i>
                                        </div>
                                    {% else %}
                                        <div class="input-status status-unverified">
                                            <i class="fas fa-exclamation-circle text-orange-500"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="field-feedback" id="email_feedback"></div>
                                {% if not user.is_email_verified %}
                                    <div class="field-help text-orange-600">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        Email not verified. <a href="#" class="text-harrier-red hover:underline">Resend verification</a>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Bio and Personal Details Section -->
                    <div class="form-section bio-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-edit text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">About You</h3>
                                <p class="section-subtitle">Tell others about yourself and your interests</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Bio -->
                            <div class="form-field form-field-full">
                                <label for="bio" class="field-label">
                                    Bio / About Me
                                </label>
                                <div class="textarea-container">
                                    <textarea name="bio"
                                              id="bio"
                                              class="form-textarea"
                                              placeholder="Write a brief description about yourself, your interests, or your business..."
                                              rows="4"
                                              maxlength="500">{{ user.bio }}</textarea>
                                    <div class="textarea-counter">
                                        <span id="bio_counter">{{ user.bio|length|default:0 }}</span>/500
                                    </div>
                                </div>
                                <div class="field-feedback" id="bio_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-lightbulb mr-1"></i>
                                    A good bio helps others understand who you are and builds trust
                                </div>
                            </div>

                            <!-- Date of Birth -->
                            <div class="form-field">
                                <label for="date_of_birth" class="field-label">
                                    Date of Birth
                                </label>
                                <div class="input-container">
                                    <input type="date"
                                           name="date_of_birth"
                                           id="date_of_birth"
                                           value="{{ user.date_of_birth|date:'Y-m-d' }}"
                                           class="form-input">
                                    <div class="input-icon">
                                        <i class="fas fa-calendar text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="date_of_birth_feedback"></div>
                            </div>

                            <!-- Gender -->
                            <div class="form-field">
                                <label for="gender" class="field-label">
                                    Gender
                                </label>
                                <div class="select-container">
                                    <select name="gender" id="gender" class="form-select">
                                        <option value="">Select Gender</option>
                                        <option value="male" {% if user.gender == 'male' %}selected{% endif %}>Male</option>
                                        <option value="female" {% if user.gender == 'female' %}selected{% endif %}>Female</option>
                                        <option value="other" {% if user.gender == 'other' %}selected{% endif %}>Other</option>
                                        <option value="prefer_not_to_say" {% if user.gender == 'prefer_not_to_say' %}selected{% endif %}>Prefer not to say</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="gender_feedback"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Tab Content -->
            <div class="tab-content" id="contact-tab" role="tabpanel" aria-labelledby="contact-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Contact Information</h2>
                    <p class="tab-description">Manage your contact details and communication preferences</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Primary Contact Section -->
                    <div class="form-section contact-primary-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Primary Contact</h3>
                                <p class="section-subtitle">Your main contact information</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Phone Number -->
                            <div class="form-field">
                                <label for="phone" class="field-label">
                                    Phone Number <span class="required">*</span>
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="phone"
                                           id="phone"
                                           value="{{ user.phone }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000"
                                           required>
                                    <div class="input-icon">
                                        <i class="fas fa-phone text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="phone_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Include country code for international numbers
                                </div>
                            </div>

                            <!-- Secondary Phone -->
                            <div class="form-field">
                                <label for="secondary_phone" class="field-label">
                                    Secondary Phone
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="secondary_phone"
                                           id="secondary_phone"
                                           value="{{ user.secondary_phone }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000">
                                    <div class="input-icon">
                                        <i class="fas fa-phone-alt text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="secondary_phone_feedback"></div>
                            </div>

                            <!-- WhatsApp Number -->
                            <div class="form-field">
                                <label for="whatsapp_number" class="field-label">
                                    WhatsApp Number
                                </label>
                                <div class="input-container">
                                    <input type="tel"
                                           name="whatsapp_number"
                                           id="whatsapp_number"
                                           value="{{ user.whatsapp_number }}"
                                           class="form-input"
                                           placeholder="+254 700 000 000">
                                    <div class="input-icon">
                                        <i class="fab fa-whatsapp text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="whatsapp_number_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Customers can contact you directly via WhatsApp
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Information Section -->
                    <div class="form-section address-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-map-marker-alt text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Address Information</h3>
                                <p class="section-subtitle">Your location and address details</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- City -->
                            <div class="form-field">
                                <label for="city" class="field-label">
                                    City
                                </label>
                                <div class="input-container">
                                    <input type="text"
                                           name="city"
                                           id="city"
                                           value="{{ user.city }}"
                                           class="form-input"
                                           placeholder="Enter your city">
                                    <div class="input-icon">
                                        <i class="fas fa-city text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="city_feedback"></div>
                            </div>

                            <!-- Country -->
                            <div class="form-field">
                                <label for="country" class="field-label">
                                    Country
                                </label>
                                <div class="select-container">
                                    <select name="country" id="country" class="form-select">
                                        <option value="Kenya" {% if user.country == 'Kenya' %}selected{% endif %}>Kenya</option>
                                        <option value="Uganda" {% if user.country == 'Uganda' %}selected{% endif %}>Uganda</option>
                                        <option value="Tanzania" {% if user.country == 'Tanzania' %}selected{% endif %}>Tanzania</option>
                                        <option value="Rwanda" {% if user.country == 'Rwanda' %}selected{% endif %}>Rwanda</option>
                                        <option value="Other" {% if user.country not in 'Kenya,Uganda,Tanzania,Rwanda' %}selected{% endif %}>Other</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="country_feedback"></div>
                            </div>

                            <!-- Full Address -->
                            <div class="form-field form-field-full">
                                <label for="address" class="field-label">
                                    Full Address
                                </label>
                                <div class="textarea-container">
                                    <textarea name="address"
                                              id="address"
                                              class="form-textarea"
                                              placeholder="Enter your complete address..."
                                              rows="3">{{ user.address }}</textarea>
                                </div>
                                <div class="field-feedback" id="address_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This helps customers locate your business or for delivery purposes
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preferences Tab Content -->
            <div class="tab-content" id="preferences-tab" role="tabpanel" aria-labelledby="preferences-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Preferences & Settings</h2>
                    <p class="tab-description">Customize your account preferences and notification settings</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Language & Localization -->
                    <div class="form-section localization-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                                <i class="fas fa-globe text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Language & Region</h3>
                                <p class="section-subtitle">Set your preferred language and regional settings</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Preferred Language -->
                            <div class="form-field">
                                <label for="preferred_language" class="field-label">
                                    Preferred Language
                                </label>
                                <div class="select-container">
                                    <select name="preferred_language" id="preferred_language" class="form-select">
                                        <option value="en" {% if user.preferred_language == 'en' %}selected{% endif %}>English</option>
                                        <option value="sw" {% if user.preferred_language == 'sw' %}selected{% endif %}>Kiswahili</option>
                                        <option value="fr" {% if user.preferred_language == 'fr' %}selected{% endif %}>Français</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="preferred_language_feedback"></div>
                            </div>

                            <!-- Timezone -->
                            <div class="form-field">
                                <label for="timezone" class="field-label">
                                    Timezone
                                </label>
                                <div class="select-container">
                                    <select name="timezone" id="timezone" class="form-select">
                                        <option value="Africa/Nairobi" {% if user.timezone == 'Africa/Nairobi' %}selected{% endif %}>East Africa Time (EAT)</option>
                                        <option value="Africa/Lagos" {% if user.timezone == 'Africa/Lagos' %}selected{% endif %}>West Africa Time (WAT)</option>
                                        <option value="UTC" {% if user.timezone == 'UTC' %}selected{% endif %}>UTC</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="timezone_feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Preferences -->
                    <div class="form-section notifications-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-yellow-500 to-yellow-600">
                                <i class="fas fa-bell text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Notification Preferences</h3>
                                <p class="section-subtitle">Choose how you want to receive notifications</p>
                            </div>
                        </div>

                        <div class="notification-options">
                            <!-- Email Notifications -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">Email Notifications</h4>
                                        <p class="option-description">Receive important updates via email</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="email_notifications" {% if user.email_notifications %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- SMS Notifications -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">SMS Notifications</h4>
                                        <p class="option-description">Get text messages for urgent updates</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="sms_notifications" {% if user.sms_notifications %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Marketing Emails -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">Marketing Emails</h4>
                                        <p class="option-description">Receive promotional offers and updates</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="marketing_emails" {% if user.marketing_emails %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Newsletter Subscription -->
                            <div class="notification-option">
                                <div class="option-content">
                                    <div class="option-info">
                                        <h4 class="option-title">Newsletter Subscription</h4>
                                        <p class="option-description">Stay updated with our monthly newsletter</p>
                                    </div>
                                    <div class="option-toggle">
                                        <label class="toggle-switch">
                                            <input type="checkbox" name="newsletter_subscription" {% if user.newsletter_subscription %}checked{% endif %}>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Privacy Settings -->
                    <div class="form-section privacy-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                                <i class="fas fa-user-shield text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Privacy Settings</h3>
                                <p class="section-subtitle">Control who can see your profile information</p>
                            </div>
                        </div>

                        <div class="form-fields-grid">
                            <!-- Profile Visibility -->
                            <div class="form-field">
                                <label for="profile_visibility" class="field-label">
                                    Profile Visibility
                                </label>
                                <div class="select-container">
                                    <select name="profile_visibility" id="profile_visibility" class="form-select">
                                        <option value="public" {% if user.profile_visibility == 'public' %}selected{% endif %}>Public - Anyone can view</option>
                                        <option value="contacts_only" {% if user.profile_visibility == 'contacts_only' %}selected{% endif %}>Contacts Only</option>
                                        <option value="private" {% if user.profile_visibility == 'private' %}selected{% endif %}>Private - Only me</option>
                                    </select>
                                    <div class="select-icon">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <div class="field-feedback" id="profile_visibility_feedback"></div>
                                <div class="field-help">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    This affects how others can find and view your profile
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Tab Content -->
            <div class="tab-content" id="security-tab" role="tabpanel" aria-labelledby="security-tab-button">
                <div class="tab-content-header">
                    <h2 class="tab-title">Security & Privacy</h2>
                    <p class="tab-description">Manage your account security and password settings</p>
                </div>

                <div class="form-sections-grid">
                    <!-- Password Management -->
                    <div class="form-section password-section">
                        <div class="section-header">
                            <div class="section-icon bg-gradient-to-br from-red-500 to-red-600">
                                <i class="fas fa-key text-white"></i>
                            </div>
                            <div class="section-info">
                                <h3 class="section-title">Password Management</h3>
                                <p class="section-subtitle">Update your password and security settings</p>
                            </div>
                        </div>

                        <div class="password-change-form">
                            <div class="form-fields-grid">
                                <!-- Current Password -->
                                <div class="form-field">
                                    <label for="current_password" class="field-label">
                                        Current Password <span class="required">*</span>
                                    </label>
                                    <div class="input-container">
                                        <input type="password"
                                               name="current_password"
                                               id="current_password"
                                               class="form-input"
                                               placeholder="Enter your current password"
                                               required>
                                        <div class="input-icon">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <button type="button" class="password-toggle" onclick="togglePassword('current_password')">
                                            <i class="fas fa-eye text-gray-400"></i>
                                        </button>
                                    </div>
                                    <div class="field-feedback" id="current_password_feedback"></div>
                                </div>

                                <!-- New Password -->
                                <div class="form-field">
                                    <label for="new_password" class="field-label">
                                        New Password <span class="required">*</span>
                                    </label>
                                    <div class="input-container">
                                        <input type="password"
                                               name="new_password"
                                               id="new_password"
                                               class="form-input"
                                               placeholder="Enter your new password"
                                               required>
                                        <div class="input-icon">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                            <i class="fas fa-eye text-gray-400"></i>
                                        </button>
                                    </div>
                                    <div class="field-feedback" id="new_password_feedback"></div>

                                    <!-- Password Strength Indicator -->
                                    <div class="password-strength">
                                        <div class="strength-bar">
                                            <div class="strength-fill" id="password_strength_fill"></div>
                                        </div>
                                        <div class="strength-text" id="password_strength_text">Password strength</div>
                                    </div>
                                </div>

                                <!-- Confirm New Password -->
                                <div class="form-field">
                                    <label for="confirm_password" class="field-label">
                                        Confirm New Password <span class="required">*</span>
                                    </label>
                                    <div class="input-container">
                                        <input type="password"
                                               name="confirm_password"
                                               id="confirm_password"
                                               class="form-input"
                                               placeholder="Confirm your new password"
                                               required>
                                        <div class="input-icon">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                            <i class="fas fa-eye text-gray-400"></i>
                                        </button>
                                    </div>
                                    <div class="field-feedback" id="confirm_password_feedback"></div>
                                </div>
                            </div>

                            <div class="password-requirements">
                                <h4 class="requirements-title">Password Requirements:</h4>
                                <ul class="requirements-list">
                                    <li class="requirement" id="req_length">
                                        <i class="fas fa-times text-red-500"></i>
                                        At least 8 characters long
                                    </li>
                                    <li class="requirement" id="req_uppercase">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains uppercase letter
                                    </li>
                                    <li class="requirement" id="req_lowercase">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains lowercase letter
                                    </li>
                                    <li class="requirement" id="req_number">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains number
                                    </li>
                                    <li class="requirement" id="req_special">
                                        <i class="fas fa-times text-red-500"></i>
                                        Contains special character
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Submit Actions -->
            <div class="form-submit-actions">
                <button type="button" class="submit-btn submit-btn-secondary" onclick="resetForm()">
                    <i class="fas fa-undo mr-2"></i>
                    Reset Changes
                </button>
                <button type="submit" class="submit-btn submit-btn-primary" id="saveProfileBtn">
                    <i class="fas fa-save mr-2"></i>
                    Save Profile
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Enhanced JavaScript for Profile Management -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all profile features
    initializeProfileFeatures();
    initializeFormValidation();
    initializePasswordStrength();
    initializeTabNavigation();
    initializeImageUpload();
    initializeAutoSave();
    initializeNotificationToggles();
    calculateProfileCompletion();
});

// Profile Features Initialization
function initializeProfileFeatures() {
    // Character counters for textareas
    const textareas = document.querySelectorAll('textarea[maxlength]');
    textareas.forEach(textarea => {
        const counter = document.getElementById(textarea.id + '_counter');
        if (counter) {
            textarea.addEventListener('input', function() {
                counter.textContent = this.value.length;

                // Update color based on length
                const maxLength = parseInt(this.getAttribute('maxlength'));
                const percentage = (this.value.length / maxLength) * 100;

                if (percentage > 90) {
                    counter.style.color = '#ef4444';
                } else if (percentage > 75) {
                    counter.style.color = '#f59e0b';
                } else {
                    counter.style.color = '#6b7280';
                }
            });
        }
    });

    // Real-time field validation
    const inputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

// Tab Navigation
function initializeTabNavigation() {
    const tabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            switchToTab(targetTab);
        });
    });
}

function switchToTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
    });

    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
        activeTab.setAttribute('aria-selected', 'true');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    const activeContent = document.getElementById(`${tabName}-tab`);
    if (activeContent) {
        activeContent.classList.add('active');
    }

    // Update mobile selector
    const mobileSelect = document.querySelector('.mobile-tab-select');
    if (mobileSelect) {
        mobileSelect.value = tabName;
    }

    // Scroll to top of form
    document.querySelector('.form-container').scrollIntoView({
        behavior: 'smooth',
        block: 'start'
    });
}

// Scroll to specific section
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        // First switch to the tab
        const tabName = sectionId.replace('-tab', '');
        switchToTab(tabName);

        // Then scroll to the section
        setTimeout(() => {
            section.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }, 300);
    }
}

// Password Toggle
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.parentElement.querySelector('.password-toggle i');

    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Password Strength Checker
function initializePasswordStrength() {
    const passwordField = document.getElementById('new_password');
    if (!passwordField) return;

    passwordField.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });
}

function checkPasswordStrength(password) {
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    // Update requirement indicators
    Object.keys(requirements).forEach(req => {
        const element = document.getElementById(`req_${req}`);
        if (element) {
            const icon = element.querySelector('i');
            if (requirements[req]) {
                element.classList.add('met');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-check');
            } else {
                element.classList.remove('met');
                icon.classList.remove('fa-check');
                icon.classList.add('fa-times');
            }
        }
    });

    // Calculate strength
    const metRequirements = Object.values(requirements).filter(Boolean).length;
    const strengthFill = document.getElementById('password_strength_fill');
    const strengthText = document.getElementById('password_strength_text');

    if (strengthFill && strengthText) {
        strengthFill.className = 'strength-fill';

        if (metRequirements === 0) {
            strengthText.textContent = 'Password strength';
        } else if (metRequirements <= 2) {
            strengthFill.classList.add('weak');
            strengthText.textContent = 'Weak password';
        } else if (metRequirements <= 3) {
            strengthFill.classList.add('fair');
            strengthText.textContent = 'Fair password';
        } else if (metRequirements <= 4) {
            strengthFill.classList.add('good');
            strengthText.textContent = 'Good password';
        } else {
            strengthFill.classList.add('strong');
            strengthText.textContent = 'Strong password';
        }
    }
}

// Image Upload Preview
function initializeImageUpload() {
    const profileInput = document.getElementById('profilePictureInput');
    if (profileInput) {
        profileInput.addEventListener('change', function() {
            previewProfileImage(this);
        });
    }
}

function previewProfileImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('profileImagePreview');
            if (preview) {
                if (preview.tagName === 'IMG') {
                    preview.src = e.target.result;
                } else {
                    // Replace placeholder with image
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'profile-image';
                    img.id = 'profileImagePreview';
                    preview.parentNode.replaceChild(img, preview);
                }
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeProfilePicture() {
    const preview = document.getElementById('profileImagePreview');
    const input = document.getElementById('profilePictureInput');

    if (preview && input) {
        // Reset input
        input.value = '';

        // Replace image with placeholder
        if (preview.tagName === 'IMG') {
            const placeholder = document.createElement('div');
            placeholder.className = 'profile-placeholder';
            placeholder.id = 'profileImagePreview';
            placeholder.innerHTML = '<i class="fas fa-user text-4xl text-gray-400"></i>';
            preview.parentNode.replaceChild(placeholder, preview);
        }
    }
}

// Form Validation
function initializeFormValidation() {
    const form = document.getElementById('profileForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });
    }
}

function validateForm() {
    let isValid = true;
    const requiredFields = document.querySelectorAll('[required]');

    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });

    // Validate password confirmation
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    if (newPassword && confirmPassword && newPassword.value) {
        if (newPassword.value !== confirmPassword.value) {
            showFieldError(confirmPassword, 'Passwords do not match');
            isValid = false;
        }
    }

    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name;

    // Clear previous errors
    clearFieldError(field);

    // Required field validation
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
        return false;
    }

    // Email validation
    if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }
    }

    // Phone validation
    if (fieldName.includes('phone') && value) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            showFieldError(field, 'Please enter a valid phone number');
            return false;
        }
    }

    return true;
}

function showFieldError(field, message) {
    const feedback = document.getElementById(field.id + '_feedback');
    if (feedback) {
        feedback.textContent = message;
        feedback.className = 'field-feedback error';
    }

    field.style.borderColor = '#ef4444';
}

function clearFieldError(field) {
    const feedback = document.getElementById(field.id + '_feedback');
    if (feedback) {
        feedback.textContent = '';
        feedback.className = 'field-feedback';
    }

    field.style.borderColor = '';
}

// Auto-save functionality
function initializeAutoSave() {
    let autoSaveTimeout;
    const form = document.getElementById('profileForm');

    if (form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    // Auto-save logic here
                    console.log('Auto-saving profile...');
                }, 2000);
            });
        });
    }
}

// Notification toggles
function initializeNotificationToggles() {
    const toggles = document.querySelectorAll('.toggle-switch input');
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            // Add visual feedback
            const option = this.closest('.notification-option');
            if (option) {
                option.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    option.style.transform = '';
                }, 150);
            }
        });
    });
}

// Profile completion calculation
function calculateProfileCompletion() {
    const fields = [
        'first_name', 'last_name', 'email', 'phone', 'bio',
        'city', 'address'
    ];

    let completed = 0;
    let total = fields.length;

    // Check profile picture
    const profilePicture = document.getElementById('profileImagePreview');
    if (profilePicture && profilePicture.tagName === 'IMG') {
        completed++;
    }
    total++;

    fields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && field.value && field.value.trim()) {
            completed++;
        }
    });

    const percentage = Math.round((completed / total) * 100);

    // Update progress indicators
    updateProgressIndicators(percentage);

    return percentage;
}

function updateProgressIndicators(percentage) {
    // Update hero section progress ring
    const heroRing = document.querySelector('.profile-avatar-section svg circle:last-child');
    if (heroRing) {
        const circumference = 2 * Math.PI * 45;
        const offset = circumference - (percentage / 100) * circumference;
        heroRing.style.strokeDashoffset = offset;
    }

    // Update completion banner
    const completionRing = document.querySelector('.progress-ring-fill');
    if (completionRing) {
        const circumference = 2 * Math.PI * 26;
        const offset = circumference - (percentage / 100) * circumference;
        completionRing.style.strokeDashoffset = offset;
    }

    // Update percentage text
    const percentageTexts = document.querySelectorAll('.progress-text span, .profile-avatar-section span');
    percentageTexts.forEach(text => {
        if (text.textContent.includes('%')) {
            text.textContent = percentage + '%';
        }
    });
}

// Form reset
function resetForm() {
    if (confirm('Are you sure you want to reset all changes? This action cannot be undone.')) {
        document.getElementById('profileForm').reset();

        // Reset profile picture preview
        const preview = document.getElementById('profileImagePreview');
        if (preview && preview.tagName === 'IMG') {
            // Restore original image or placeholder
            location.reload();
        }

        // Clear all feedback messages
        document.querySelectorAll('.field-feedback').forEach(feedback => {
            feedback.textContent = '';
            feedback.className = 'field-feedback';
        });

        // Reset password strength
        const strengthFill = document.getElementById('password_strength_fill');
        const strengthText = document.getElementById('password_strength_text');
        if (strengthFill && strengthText) {
            strengthFill.className = 'strength-fill';
            strengthText.textContent = 'Password strength';
        }

        // Reset requirements
        document.querySelectorAll('.requirement').forEach(req => {
            req.classList.remove('met');
            const icon = req.querySelector('i');
            icon.classList.remove('fa-check');
            icon.classList.add('fa-times');
        });
    }
}

// Save profile
function saveProfile() {
    const form = document.getElementById('profileForm');
    if (form && validateForm()) {
        form.submit();
    }
}
</script>

{% endblock %}
